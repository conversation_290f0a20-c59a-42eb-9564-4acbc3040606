package com.example.addon.mixin;

import com.example.addon.utils.RotationManager;
import meteordevelopment.meteorclient.MeteorClient;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to add ROTATIONGRIM field to MeteorClient class
 */
@Mixin(MeteorClient.class)
public class MeteorClientMixin {
    
    /**
     * 添加 ROTATIONGRIM 静态字段到 MeteorClient 类
     * 使用懒加载单例模式
     */
    @Unique
    public static RotationManager ROTATIONGRIM = RotationManager.getInstance();
    
    /**
     * 在 MeteorClient 初始化时确保 ROTATIONGRIM 已经初始化
     */
    @Inject(method = "<init>", at = @At("TAIL"))
    private void onMeteorClientInit(CallbackInfo ci) {
        // 确保 ROTATIONGRIM 实例已经创建
        if (ROTATIONGRIM == null) {
            ROTATIONGRIM = RotationManager.getInstance();
        }
    }
}
