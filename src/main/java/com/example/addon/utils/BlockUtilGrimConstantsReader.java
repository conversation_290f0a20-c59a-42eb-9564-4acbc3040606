package com.example.addon.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

public class BlockUtilGrimConstantsReader {

    /**
     * 读取 BaritoneUtil 类内所有 ConstantPool 字段的值
     *
     * @return Map<字段名, 值>
     */
    public static Map<String, Object> readConstants() {
        Map<String, Object> map = new LinkedHashMap<>();
        try {
            Class<?> clazz = Class.forName("meteordevelopment.meteorclient.utils.world.BaritoneUtil");

            String[] fieldsToRead = new String[]{
                "const_kHyleDNJG5B4uJ6",
                "const_Slmo4OM8l7Ygd6t",
                "const_kAoMdsM3a9a9KxQ",
                "const_Pq3IHaA6Bbqcd2V",
                "const_9ns4yAlKrc6ld9e",
                "const_cyP1ut2d3kobr5A",
                "const_odXVnbAldIlBZyD",
                "const_EoGBUm9ZtTZdRM0",
                "const_aT3wGe2eyCDSynL",
                "const_9zESTdm2W0rZQMH",
                "const_9NlVNacaaQNx66I",
                "const_cxvCdIeEMrpY9No",
                "const_YjbvvMDFWrqOxKs",
                "const_AmkJGNWj2MVGL4H",
                "const_gBqjx3PljQsWyjJ",
                "const_t0Ca1TyhobkWg5t",
                "const_wr29CELQ5jxeFIZ"
            };

            for (String fieldName : fieldsToRead) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object val = field.get(null);
                    map.put(fieldName, val);
                } catch (NoSuchFieldException e) {
                    map.put(fieldName, "<Field Not Found>");
                } catch (Exception e) {
                    map.put(fieldName, "<Access Error: " + e.getMessage() + ">");
                }
            }
        } catch (ClassNotFoundException e) {
            map.put("Class", "<Class Not Found>");
        }
        return map;
    }

    public static void read() {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        Map<String, Object> constants = readConstants();

        try (FileWriter writer = new FileWriter("D:/map.json")) {
            gson.toJson(constants, writer);
            System.out.println("Constants saved to D:/map.json");
        } catch (IOException e) {
            System.err.println("Error writing to file: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

