package com.example.addon.utils;

import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.utils.player.Rotations;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.Full;

/**
 * 旋转管理器 - 用于管理玩家视角旋转的核心组件
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>统一管理所有模块的视角旋转需求</li>
 *   <li>基于优先级系统处理旋转冲突</li>
 *   <li>提供平滑的视角过渡和同步机制</li>
 *   <li>确保旋转操作的线程安全性</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>自动建造模块需要精确朝向目标方块</li>
 *   <li>战斗模块需要快速转向敌人</li>
 *   <li>种植模块需要面向种植位置</li>
 *   <li>挖掘模块需要对准目标方块</li>
 * </ul>
 *
 * <p>工作原理：</p>
 * <ol>
 *   <li>模块通过 {@link #register(Rotation)} 注册旋转请求</li>
 *   <li>系统根据优先级决定是否接受请求</li>
 *   <li>发送网络包更新服务器端玩家朝向</li>
 *   <li>同步客户端摄像机视角</li>
 *   <li>操作完成后调用 {@link #sync()} 恢复原始朝向</li>
 * </ol>
 *
 * <AUTHOR> Helper
 * @since 1.0.0
 */
public class RotationManager {
    MinecraftClient mc = MinecraftClient.getInstance();

    // 单例实例，使用 volatile 确保多线程环境下的可见性
    private static volatile RotationManager instance;

    public Rotation currentRotation = null;
    Timer timer = new Timer();

    // 私有构造函数，防止外部直接实例化
    private RotationManager() {
        MeteorClient.EVENT_BUS.subscribe(this);
        mc = MinecraftClient.getInstance();

    }

    /**
     * 获取 RotationManager 的单例实例
     * 使用双重检查锁定模式实现线程安全的懒加载
     *
     * @return RotationManager 的唯一实例
     */
    public static RotationManager getInstance() {
        if (instance == null) {
            synchronized (RotationManager.class) {
                if (instance == null) {
                    instance = new RotationManager();
                }
            }
        }
        return instance;
    }

    public boolean register(Rotation rotation) {
        if (this.currentRotation != null && this.currentRotation.getPriority() > rotation.getPriority()) {
            return false;
        } else {
            this.currentRotation = rotation;
            this.timer.reset();
            mc.player
                .networkHandler
                .sendPacket(
                    new Full(
                        mc.player.getX(),
                        mc.player.getY(),
                        mc.player.getZ(),
                        rotation.getYaw(),
                        rotation.getPitch(),
                        mc.player.isOnGround()
                    )
                );
            Rotations.setCamRotation(rotation.getYaw(), rotation.getPitch());
            return true;
        }
    }

    public void sync() {
        mc.player
            .networkHandler
            .sendPacket(
                new Full(
                    mc.player.getX(),
                    mc.player.getY(),
                    mc.player.getZ(),
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    mc.player.isOnGround()
                )
            );
        this.currentRotation = null;
    }
}
